package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.CartService;
import com.gianghp.dried_tea_shop.service.CartItemService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/cart")
@RequiredArgsConstructor
@Slf4j
public class CartController {
    
    private final CartService cartService;
    private final CartItemService cartItemService;
    
    // TODO: Implement cart REST endpoints
    // GET /api/cart - Get current user's cart
    // POST /api/cart/items - Add item to cart
    // PUT /api/cart/items/{itemId} - Update cart item quantity
    // DELETE /api/cart/items/{itemId} - Remove item from cart
    // DELETE /api/cart/clear - Clear cart
    // GET /api/cart/total - Get cart total amount
    // GET /api/cart/count - Get cart items count
    // POST /api/cart/validate - Validate cart items availability
}
