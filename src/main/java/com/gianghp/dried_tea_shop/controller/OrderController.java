package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.OrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@Slf4j
public class OrderController {
    
    private final OrderService orderService;
    
    // TODO: Implement order REST endpoints
    // POST /api/orders - Create order from cart
    // GET /api/orders - Get current user's orders with pagination
    // GET /api/orders/{id} - Get order by ID
    // PUT /api/orders/{id}/cancel - Cancel order
    // GET /api/orders/{id}/track - Track order
    // GET /api/orders/admin - Get all orders (admin only)
    // PUT /api/orders/{id}/status - Update order status (admin only)
    // GET /api/orders/reports/revenue - Get revenue report (admin only)
    // GET /api/orders/reports/sales - Get sales report (admin only)
    // GET /api/orders/recent - Get recent orders (admin only)
}
