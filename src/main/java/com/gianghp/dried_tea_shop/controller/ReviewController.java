package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.ReviewService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/reviews")
@RequiredArgsConstructor
@Slf4j
public class ReviewController {
    
    private final ReviewService reviewService;
    
    // TODO: Implement review REST endpoints
    // POST /api/reviews - Create review
    // PUT /api/reviews/{id} - Update review (own review only)
    // DELETE /api/reviews/{id} - Delete review (own review or admin)
    // GET /api/reviews/product/{productId} - Get reviews for product
    // GET /api/reviews/user/{userId} - Get reviews by user
    // GET /api/reviews/my-reviews - Get current user's reviews
    // GET /api/reviews/{id} - Get review by ID
    // GET /api/reviews/recent - Get recent reviews
    // GET /api/reviews/top-rated - Get top rated reviews
    // GET /api/reviews/admin - Get all reviews for moderation (admin only)
    // PUT /api/reviews/{id}/moderate - Moderate review (admin only)
}
