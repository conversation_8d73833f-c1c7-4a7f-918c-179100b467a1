package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
public class UserController {
    
    private final UserService userService;
    
    // TODO: Implement user REST endpoints
    // GET /api/users - Get all users (admin only)
    // GET /api/users/{id} - Get user by ID
    // GET /api/users/profile - Get current user profile
    // PUT /api/users/profile - Update current user profile
    // PUT /api/users/{id} - Update user (admin only)
    // DELETE /api/users/{id} - Delete user (admin only)
    // PUT /api/users/change-password - Change password
    // GET /api/users/search - Search users (admin only)
    // PUT /api/users/{id}/activate - Activate user (admin only)
    // PUT /api/users/{id}/deactivate - Deactivate user (admin only)
}
