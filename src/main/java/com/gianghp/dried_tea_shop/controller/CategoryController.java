package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.common.enums.ErrorCode;
import com.gianghp.dried_tea_shop.common.response.ApiPageResponse;
import com.gianghp.dried_tea_shop.common.response.ApiResponse;
import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.service.CategoryService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/categories")
@RequiredArgsConstructor
@Slf4j
public class CategoryController {
    
    private final CategoryService categoryService;

    @GetMapping
    public ResponseEntity<ApiResponse<List<CategoryResponseDto>>> getAllCategories() {
        try {
            return ResponseEntity.ok(ApiResponse.success(categoryService.getAllCategories()));
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(ApiResponse.error(ErrorCode.INTERNAL_SERVER_ERROR));
        }
    }

    
    // TODO: Implement category REST endpoints
    // GET /api/categories - Get all categories
    // GET /api/categories/{id} - Get category by ID
    // POST /api/categories - Create new category (admin only)
    // PUT /api/categories/{id} - Update category (admin only)
    // DELETE /api/categories/{id} - Delete category (admin only)
    // GET /api/categories/search - Search categories
    // GET /api/categories/{id}/products - Get products in category
    // GET /api/categories/with-products - Get categories with products
    // GET /api/categories/statistics - Get category statistics (admin only)
}
