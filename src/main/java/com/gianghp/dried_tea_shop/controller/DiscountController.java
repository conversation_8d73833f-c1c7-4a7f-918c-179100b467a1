package com.gianghp.dried_tea_shop.controller;

import com.gianghp.dried_tea_shop.service.DiscountService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/discounts")
@RequiredArgsConstructor
@Slf4j
public class DiscountController {
    
    private final DiscountService discountService;
    
    // TODO: Implement discount REST endpoints
    // GET /api/discounts - Get all discounts (admin only)
    // GET /api/discounts/{id} - Get discount by ID (admin only)
    // POST /api/discounts - Create new discount (admin only)
    // PUT /api/discounts/{id} - Update discount (admin only)
    // DELETE /api/discounts/{id} - Delete discount (admin only)
    // GET /api/discounts/active - Get active discounts
    // GET /api/discounts/current - Get current active discounts
    // GET /api/discounts/upcoming - Get upcoming discounts
    // GET /api/discounts/expired - Get expired discounts (admin only)
    // PUT /api/discounts/{id}/activate - Activate discount (admin only)
    // PUT /api/discounts/{id}/deactivate - Deactivate discount (admin only)
    // POST /api/discounts/{id}/apply-products - Apply discount to products (admin only)
    // DELETE /api/discounts/{id}/remove-products - Remove discount from products (admin only)
}
