package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.Cart;
import com.gianghp.dried_tea_shop.repository.CartRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartService {
    
    private final CartRepository cartRepository;
    
    // TODO: Implement cart service methods
    // - Create cart for user
    // - Find cart by user ID
    // - Find cart by user email
    // - Clear cart
    // - Delete cart
    // - Get cart total amount
    // - Get cart total items
    // - Get cart total quantity
    // - Validate cart
    // - Merge carts (for guest to user conversion)
    // - Check cart availability
}
