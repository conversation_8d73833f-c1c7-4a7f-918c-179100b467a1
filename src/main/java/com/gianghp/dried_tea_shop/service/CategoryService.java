package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.category.CategoryResponseDto;
import com.gianghp.dried_tea_shop.entity.Category;
import com.gianghp.dried_tea_shop.mapper.CategoryMapper;
import com.gianghp.dried_tea_shop.repository.CategoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CategoryService {

  private final CategoryRepository categoryRepository;
  private final CategoryMapper categoryMapper;

  public List<CategoryResponseDto> getAllCategories() {
    return categoryRepository.findAll().stream()
        .map(categoryMapper::toResponseDto)
        .toList();

  }

  // TODO: Implement category service methods
  // - Create category
  // - Update category
  // - Delete category
  // - Find category by ID
  // - Find category by name
  // - Find all categories
  // - Find categories with products
  // - Find categories without products
  // - Search categories by keyword
  // - Get category statistics
}
