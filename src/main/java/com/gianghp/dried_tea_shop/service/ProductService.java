package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.dto.product.ProductRequestDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.entity.Product;
import com.gianghp.dried_tea_shop.mapper.ProductMapper;
import com.gianghp.dried_tea_shop.repository.ProductRepository;
import com.gianghp.dried_tea_shop.specification.ProductSpecification;
import java.math.BigDecimal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProductService {
    
    private final ProductRepository productRepository;
    private final ProductMapper productMapper;

    public Page<ProductResponseDto> getAllProducts(
        String name,
        String categoryId,
        String status,
        BigDecimal minPrice,
        BigDecimal maxPrice,
        BigDecimal minRating,
        Integer page,
        Integer size,
        String sortBy
    ) {
        PageRequest pageable = PageRequest.of(page, size, Sort.by(sortBy).descending());

        return productRepository.findAll(
            ProductSpecification.filter(name, categoryId, status, minPrice, maxPrice, minRating),
            pageable
        ).map(productMapper::toResponseDto);
    }

    public ProductResponseDto getProductById(UUID id) {
        return productRepository.findById(id)
            .map(productMapper::toResponseDto)
            .orElseThrow(() -> new RuntimeException("Product not found"));
    }

    // TODO: Implement product service methods
    // - Create product
    // - Update product
    // - Delete product
    // - Find product by ID
    // - Find all products with pagination
    // - Find products by category
    // - Find products by status
    // - Search products by keyword
    // - Find products by price range
    // - Find products with low stock
    // - Find top rated products
    // - Find most reviewed products
    // - Update product stock
    // - Update product rating
    // - Apply discount to product
    // - Remove discount from product
}
