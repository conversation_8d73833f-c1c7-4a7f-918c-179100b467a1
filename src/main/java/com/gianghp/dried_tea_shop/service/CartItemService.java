package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.CartItem;
import com.gianghp.dried_tea_shop.repository.CartItemRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartItemService {
    
    private final CartItemRepository cartItemRepository;
    
    // TODO: Implement cart item service methods
    // - Add item to cart
    // - Update item quantity
    // - Remove item from cart
    // - Find cart items by cart ID
    // - Find cart items by user ID
    // - Find cart item by cart and product
    // - Clear cart items
    // - Validate cart item
    // - Check product availability
    // - Calculate item total price
    // - Merge duplicate items
}
