package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.User;
import com.gianghp.dried_tea_shop.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class UserService {
    
    private final UserRepository userRepository;
    
    // TODO: Implement user service methods
    // - Create user
    // - Update user
    // - Delete user
    // - Find user by ID
    // - Find user by email
    // - Find all users
    // - Find users by role
    // - Validate user credentials
    // - Change password
    // - Update profile
    // - Activate/deactivate user
}
