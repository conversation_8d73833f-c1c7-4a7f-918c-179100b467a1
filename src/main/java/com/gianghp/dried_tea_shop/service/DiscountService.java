package com.gianghp.dried_tea_shop.service;

import com.gianghp.dried_tea_shop.entity.Discount;
import com.gianghp.dried_tea_shop.repository.DiscountRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DiscountService {
    
    private final DiscountRepository discountRepository;
    
    // TODO: Implement discount service methods
    // - Create discount
    // - Update discount
    // - Delete discount
    // - Find discount by ID
    // - Find discount by name
    // - Find all discounts
    // - Find active discounts
    // - Find current active discounts
    // - Find expired discounts
    // - Find upcoming discounts
    // - Activate discount
    // - Deactivate discount
    // - Apply discount to products
    // - Remove discount from products
    // - Calculate discount amount
    // - Validate discount dates
}
