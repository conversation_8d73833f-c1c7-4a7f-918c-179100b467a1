package com.gianghp.dried_tea_shop.mapper;

import com.gianghp.dried_tea_shop.dto.product.ProductRequestDto;
import com.gianghp.dried_tea_shop.dto.product.ProductResponseDto;
import com.gianghp.dried_tea_shop.entity.Product;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring", uses = {ImageMapper.class, UuidMapper.class})
public interface ProductMapper {

    @Mapping(source = "id", target = "productId", qualifiedByName = "uuidToString")
    @Mapping(source = "category.name", target = "categoryName")
    @Mapping(source = "discount.value", target = "discountValue")
    @Mapping(source = "image", target = "image")
    @Mapping(source = "createdAt", target = "createdAt", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
    ProductResponseDto toResponseDto(Product product);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "reviewCount", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "discount", ignore = true)
    @Mapping(target = "image", ignore = true)
    Product toEntity(ProductRequestDto productRequestDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "reviewCount", ignore = true)
    @Mapping(target = "category", ignore = true)
    @Mapping(target = "discount", ignore = true)
    @Mapping(target = "image", ignore = true)
    void updateEntityFromDto(ProductRequestDto productRequestDto, @MappingTarget Product product);
}
